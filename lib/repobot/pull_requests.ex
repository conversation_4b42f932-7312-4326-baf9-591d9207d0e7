defmodule Repobot.PullRequests do
  @moduledoc """
  The PullRequests context.
  """

  import Ecto.Query, warn: false
  alias Repobot.Repo

  alias Repobot.PullRequest

  @doc """
  Returns a pull request matching the given options.
  """
  def get_pull_request_by(opts) do
    Repo.get_by(PullRequest, opts)
  end

  @doc """
  Creates a pull request with the given attributes.
  """
  def create_pull_request(attrs) do
    %PullRequest{}
    |> PullRequest.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates a pull request with the given attributes and associates it with source files.
  """
  def create_pull_request_with_source_files(attrs, source_file_ids \\ []) do
    Repo.transaction(fn ->
      case create_pull_request(attrs) do
        {:ok, pull_request} ->
          if length(source_file_ids) > 0 do
            case associate_source_files(pull_request, source_file_ids) do
              {:ok, pull_request} -> pull_request
              {:error, reason} -> Repo.rollback(reason)
            end
          else
            pull_request
          end

        {:error, changeset} ->
          Repo.rollback(changeset)
      end
    end)
  end

  @doc """
  Associates a pull request with source files.
  """
  def associate_source_files(%PullRequest{} = pull_request, source_file_ids) do
    # Use the proper content projection query when loading source files
    source_files = Repobot.SourceFiles.list_source_files_by_ids(source_file_ids)

    pull_request
    |> Repo.preload(:source_files)
    |> Ecto.Changeset.change()
    |> Ecto.Changeset.put_assoc(:source_files, source_files)
    |> Repo.update()
  end

  @doc """
  Updates a pull request with the given attributes.
  """
  def update_pull_request(%PullRequest{} = pull_request, attrs) do
    pull_request
    |> PullRequest.changeset(attrs)
    |> Repo.update()
  end
end
